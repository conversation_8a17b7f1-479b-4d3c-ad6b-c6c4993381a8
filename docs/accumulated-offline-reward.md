# 累积离线奖励功能文档

## 概述

累积离线奖励功能允许用户的离线奖励持续累积，而不是每次获取时重新计算。用户可以在任何时候查看累积的奖励，并选择何时领取。

## 核心特性

### 1. 奖励累积机制
- 用户离线超过2分钟后开始累积奖励
- 奖励会自动累积到 `UserWallet.accumulatedOfflineGems` 字段
- 每次调用获取离线奖励接口时，系统会计算新的奖励并累加到现有奖励中
- 最大累积时间限制为8小时

### 2. 基于 farm_configs 表的计算
- **数据源**: 使用 `farm_configs` 表中的配置数据
- **offline 字段**: 每个等级配置包含 `offline` 字段，表示该等级每秒的GEM收益率
- **动态配置**: 支持通过管理接口动态调整离线奖励率
- **精确计算**: 避免复杂的生产链计算，直接基于配置的收益率

### 3. 数据库字段
在 `UserWallet` 模型中新增了以下字段：
- `accumulatedOfflineGems`: 累积的离线宝石奖励（DECIMAL(65,3)）
- `lastOfflineRewardCalculation`: 上次离线奖励计算时间（DATE）
- `lastActiveTime`: 最后活跃时间（DATE）

### 4. 计算逻辑
- **基于 `farm_configs` 表**: 使用最新的农场配置数据计算离线奖励
- **精确的每秒收益率**: 直接使用配置表中的 `offline` 字段（每秒GEM收益率）
- **简化计算公式**: 离线收益 = offline字段值 × 离线时间（秒）
- **支持多农场区块**: 累加所有已解锁农场区块的离线收益
- **配置驱动**: 奖励计算完全基于数据库配置，支持动态调整

## API 接口

### 1. 获取累积离线奖励信息

**接口**: `GET /api/wallet/offline-reward`

**功能**: 查看当前累积的离线奖励（不消耗奖励）

**响应示例**:
```json
{
  "success": true,
  "data": {
    "isOffline": true,
    "offlineTime": 7200,
    "accumulatedOfflineReward": {
      "gem": 150.500
    },
    "lastActiveTime": "2025-07-23T08:00:00.000Z",
    "lastCalculationTime": "2025-07-23T10:00:00.000Z"
  }
}
```

### 2. 领取累积离线奖励

**接口**: `POST /api/wallet/claim-offline-reward`

**功能**: 实际领取累积的离线奖励并更新用户资源

**响应示例**:
```json
{
  "success": true,
  "data": {
    "claimedReward": {
      "gem": 150.500
    },
    "remainingReward": {
      "gem": 0.000
    },
    "currentGem": 1250.750
  }
}
```

## 工作流程

### 1. 奖励累积过程
1. 用户调用 `GET /api/wallet/offline-reward` 接口
2. 系统检查用户是否离线（超过2分钟未活跃）
3. 如果离线，获取用户所有已解锁的农场区块
4. 从 `farm_configs` 表获取激活的配置数据
5. 计算新奖励：遍历每个农场区块，使用对应等级的 `offline` 字段值 × 时间间隔
6. 将新奖励累加到 `accumulatedOfflineGems` 字段
7. 更新 `lastOfflineRewardCalculation` 时间
8. 返回累积奖励信息

### 2. 奖励领取过程
1. 用户调用 `POST /api/wallet/claim-offline-reward` 接口
2. 系统先更新累积奖励（确保最新）
3. 将累积奖励添加到用户的GEM余额
4. 清空 `accumulatedOfflineGems` 字段
5. 更新 `lastActiveTime` 为当前时间
6. 创建钱包历史记录
7. 返回领取结果

## 关键优势

### 1. 持续累积
- 奖励不会因为用户未及时领取而丢失
- 支持长时间离线后的奖励累积

### 2. 灵活领取
- 用户可以选择何时领取累积的奖励
- 支持查看累积奖励而不立即领取

### 3. 数据一致性
- 使用数据库事务确保奖励计算和领取的原子性
- 防止重复计算和奖励丢失

### 4. 性能优化
- 避免每次都重新计算完整的离线时间
- 只计算增量奖励并累加

## 注意事项

### 1. 时间限制
- 单次离线奖励计算最多8小时
- 超过8小时的离线时间不会产生额外奖励

### 2. 活跃状态判断
- 通过 `lastActiveTime` 字段判断用户是否离线
- 调用 `/api/wallet/increase-gem` 接口会更新活跃时间

### 3. 兼容性
- 保持与现有离线奖励系统的兼容性
- 支持从旧系统平滑迁移到累积系统

## 测试建议

1. 测试奖励累积功能：多次调用获取接口，验证奖励是否正确累积
2. 测试奖励领取功能：验证领取后奖励是否正确添加到余额
3. 测试边界条件：验证最大时间限制和零奖励情况
4. 测试并发安全：验证多个请求同时处理时的数据一致性
