# 离线奖励计算方式对比

## 概述

本文档对比了旧的基于 `TimeWarpService` 的离线奖励计算方式和新的基于 `farm_configs` 表的计算方式。

## 旧方式：基于 TimeWarpService

### 计算逻辑
- 使用 `TimeWarpService.calculateTimeWarpRewards` 方法
- 模拟完整的生产→出货→获得GEM流程
- 复杂的多步骤计算过程

### 计算步骤
1. 计算农场区块的牛奶生产量
2. 计算出货线的牛奶处理能力
3. 取两者中的较小值作为实际处理量
4. 将处理的牛奶转换为GEM
5. 应用VIP和速度加成

### 优点
- 模拟真实的游戏机制
- 考虑了生产和处理的平衡
- 自动应用各种加成效果

### 缺点
- 计算复杂，性能开销大
- 依赖多个系统组件
- 难以精确控制奖励率
- 不易于配置和调整

## 新方式：基于 farm_configs 表

### 计算逻辑
- 直接使用 `farm_configs` 表中的 `offline` 字段
- 简单的乘法计算：`offline值 × 时间（秒）`
- 累加所有已解锁农场区块的收益

### 计算步骤
1. 获取用户所有已解锁的农场区块
2. 从 `farm_configs` 表获取对应等级的配置
3. 使用配置中的 `offline` 字段值（每秒GEM收益率）
4. 计算：`离线收益 = offline × 离线时间（秒）`
5. 累加所有农场区块的收益

### 优点
- 计算简单，性能优异
- 配置驱动，易于调整
- 精确控制奖励率
- 独立性强，不依赖其他系统
- 支持动态配置更新

### 缺点
- 不模拟真实的生产流程
- 需要手动配置每个等级的奖励率
- 不自动应用VIP和加成效果（如需要可在配置中体现）

## 配置示例

### farm_configs 表结构
```sql
CREATE TABLE farm_configs (
  id INT PRIMARY KEY,
  grade INT,           -- 等级
  offline DECIMAL(10,3), -- 每秒离线GEM收益率
  -- 其他字段...
);
```

### 配置数据示例
```sql
INSERT INTO farm_configs (grade, offline) VALUES
(1, 0.100),  -- 等级1：每秒0.1 GEM
(2, 0.150),  -- 等级2：每秒0.15 GEM
(3, 0.225),  -- 等级3：每秒0.225 GEM
-- ...
```

## 计算示例

### 场景
- 用户有2个已解锁的农场区块
- 区块1：等级1（offline = 0.100 GEM/秒）
- 区块2：等级3（offline = 0.225 GEM/秒）
- 离线时间：3600秒（1小时）

### 新方式计算
```
区块1收益 = 0.100 × 3600 = 360 GEM
区块2收益 = 0.225 × 3600 = 810 GEM
总收益 = 360 + 810 = 1170 GEM
```

### 优势对比
- **性能**: 新方式计算速度快数倍
- **可控性**: 可精确设定每个等级的奖励率
- **维护性**: 配置更改无需代码修改
- **稳定性**: 不依赖复杂的业务逻辑

## 迁移说明

### AccumulatedOfflineRewardService 更新
- 已将 `calculateOfflineRewardForPeriod` 方法从使用 `TimeWarpService` 改为使用 `farm_configs`
- 保持了累积奖励的核心功能
- 提升了计算性能和配置灵活性

### 兼容性
- API接口保持不变
- 响应格式保持兼容
- 用户体验无变化

### 配置要求
- 确保 `farm_configs` 表有完整的等级配置
- 每个等级都需要设置合适的 `offline` 值
- 建议根据游戏平衡性调整奖励率

## 总结

新的基于 `farm_configs` 的离线奖励计算方式在保持功能完整性的同时，显著提升了性能和可维护性。通过配置驱动的方式，运营团队可以更灵活地调整游戏平衡，而开发团队则获得了更简洁和稳定的代码实现。
