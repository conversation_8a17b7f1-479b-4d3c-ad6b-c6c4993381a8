# 时间跳跃道具和加速道具购买及使用机制修改

## 概述

本文档描述了对时间跳跃道具和加速道具购买及使用机制的重要修改，主要包括移除自动使用逻辑、修改记录创建方式，以及新增手动使用接口。

## 主要修改内容

### 1. 时间跳跃道具处理机制修改

#### 修改前
- 时间跳跃道具（time_jump）购买后立即自动使用
- 在 `ActiveBooster` 表中记录持续状态
- VIP会员购买中包含的时间跳跃奖励自动触发

#### 修改后
- 时间跳跃道具购买后不再自动使用，存储在用户背包中
- 不在 `ActiveBooster` 表中记录状态（因为是即时消耗型道具）
- 用户需要通过专门的接口手动触发时间跳跃功能
- VIP会员购买中的时间跳跃奖励也改为手动使用

### 2. Booster记录创建逻辑修改

#### 修改前
```javascript
// 如果存在相同类型的道具，增加数量
if (existingBooster) {
  existingBooster.quantity += (product.quantity || 1);
  await existingBooster.save({ transaction });
}
```

#### 修改后
```javascript
// 每次购买都创建新的道具记录
await Booster.create({
  walletId,
  type: product.type,
  multiplier: product.multiplier,
  duration: product.duration,
  quantity: product.quantity || 1
}, { transaction });
```

#### 优势
- 每个购买行为都有独立的记录
- 便于追踪和管理
- 避免数量累加可能导致的问题

### 3. 新增手动使用时间跳跃道具接口

#### 接口信息
- **路径**: `POST /api/iap/time-warp/use`
- **功能**: 手动使用时间跳跃道具
- **参数**: `{ boosterId: number }`

#### 请求示例
```javascript
POST /api/iap/time-warp/use
Headers: {
  'wallet-address': 'user_wallet_address',
  'wallet-signature': 'user_signature'
}
Body: {
  "boosterId": 123
}
```

#### 响应示例
```json
{
  "ok": true,
  "message": "时间跳跃执行成功",
  "rewards": {
    "gemsEarned": 150.500,
    "milkProduced": 18000.000,
    "milkProcessed": 14400.000,
    "farmProductionPerSecond": 2.500,
    "deliveryProcessingPerSecond": 2.000,
    "hasVip": false,
    "hasSpeedBoost": false,
    "speedBoostMultiplier": 1.00
  },
  "remainingQuantity": 2
}
```

## 工作流程

### 购买时间跳跃道具
1. 用户购买时间跳跃道具
2. 系统创建新的 `Booster` 记录
3. 道具存储在用户背包中，不自动使用

### 使用时间跳跃道具
1. 用户调用 `GET /api/iap/boosters` 查看拥有的道具
2. 用户选择要使用的时间跳跃道具
3. 用户调用 `POST /api/iap/time-warp/use` 手动使用道具
4. 系统执行时间跳跃计算并发放奖励
5. 道具数量减1，如果数量为0则删除记录

### 预览功能
- 用户可以通过 `GET /api/iap/time-warp/preview?hours=X` 预览时间跳跃收益
- 预览不消耗道具，仅用于展示预期收益

## 时间跳跃计算逻辑验证

### 计算公式
1. **农场生产**: `farmProductionPerSecond = Σ(每个农场区块的每秒产量)`
2. **VIP农场加成**: `farmProductionPerSecond *= 1.3` (如果有VIP)
3. **出货线处理**: `deliveryProcessingPerSecond = blockUnit / deliverySpeed`
4. **VIP出货加成**: `deliveryProcessingPerSecond *= 1.3` (如果有VIP)
5. **速度道具加成**: `deliveryProcessingPerSecond *= speedBoostMultiplier`
6. **时间计算**: `timeInSeconds = hours * 3600`
7. **牛奶生产**: `milkProduced = farmProductionPerSecond * timeInSeconds`
8. **牛奶处理**: `milkProcessed = min(milkProduced, deliveryProcessingPerSecond * timeInSeconds)`
9. **方块生产**: `blocksProduced = floor(milkProcessed / blockUnit)`
10. **GEM收益**: `gemsEarned = blocksProduced * blockPrice * vipPriceMultiplier`

### VIP加成效果
- **农场生产速度**: +30%
- **出货线速度**: +30%
- **方块价格**: +20%

### 速度道具效果
- **出货线速度**: 根据道具倍数增加

## 受影响的文件

### 控制器文件
- `src/controllers/dappPortalPaymentController.ts`
  - 移除时间跳跃道具的自动使用逻辑
  - 修改Booster记录创建方式
- `src/controllers/iapController.ts`
  - 新增 `useTimeWarpBooster` 方法

### 路由文件
- `src/routes/iapRoutes.ts`
  - 新增 `POST /api/iap/time-warp/use` 路由

### 服务文件
- `src/services/timeWarpService.ts`
  - 保持现有的计算逻辑不变
  - 支持手动调用

## 测试建议

### 功能测试
1. **购买测试**: 验证购买时间跳跃道具后不会自动使用
2. **记录测试**: 验证每次购买都创建新的Booster记录
3. **手动使用测试**: 验证手动使用接口的正确性
4. **奖励计算测试**: 验证时间跳跃奖励计算的准确性
5. **道具消耗测试**: 验证使用后道具数量正确减少

### 边界测试
1. **无效道具ID**: 测试使用不存在的道具ID
2. **数量不足**: 测试使用已消耗完的道具
3. **权限验证**: 测试使用其他用户的道具
4. **并发使用**: 测试同时使用多个道具的情况

## 向后兼容性

### API兼容性
- 现有的预览接口保持不变
- 购买接口响应格式保持不变
- 道具列表接口保持不变

### 数据兼容性
- 现有的Booster记录继续有效
- 时间跳跃历史记录格式不变
- VIP和速度加成逻辑保持一致

## 注意事项

1. **用户体验**: 需要在前端添加手动使用时间跳跃道具的界面
2. **提示信息**: 建议在购买时提示用户道具需要手动使用
3. **道具管理**: 用户可能需要更好的道具管理界面
4. **批量使用**: 未来可能需要支持批量使用多个时间跳跃道具

## 总结

这次修改将时间跳跃道具从自动使用改为手动使用，提供了更好的用户控制体验，同时简化了Booster记录的管理逻辑。修改后的系统更加灵活和可控，为未来的功能扩展奠定了基础。
