// 测试时间跳跃功能并查看调试信息
const axios = require('axios');

const BASE_URL = 'http://localhost:3456/api';

// 测试用的钱包地址和签名（请替换为实际的测试数据）
const TEST_WALLET_ADDRESS = 'EQD4FPq-PRDieyQKkizFTRtSDyucUIqrj0v_zXJmqaDp6_0t';
const TEST_SIGNATURE = 'test_signature';

async function testTimeWarpDebug() {
    try {
        console.log('🧪 开始测试时间跳跃功能（调试模式）...\n');

        // 1. 获取用户基本信息
        console.log('👤 获取用户基本信息...');
        const walletResponse = await axios.get(`${BASE_URL}/wallet`, {
            headers: {
                'wallet-address': TEST_WALLET_ADDRESS,
                'wallet-signature': TEST_SIGNATURE
            }
        });

        console.log('用户钱包信息:');
        console.log(`- GEM余额: ${walletResponse.data.data.gem}`);
        console.log(`- 牛奶余额: ${walletResponse.data.data.pendingMilk}`);
        console.log('');

        // 2. 获取农场区块信息
        console.log('🏡 获取农场区块信息...');
        const farmPlotsResponse = await axios.get(`${BASE_URL}/farm-plots`, {
            headers: {
                'wallet-address': TEST_WALLET_ADDRESS,
                'wallet-signature': TEST_SIGNATURE
            }
        });

        console.log('农场区块状态:');
        if (farmPlotsResponse.data.data && farmPlotsResponse.data.data.length > 0) {
            farmPlotsResponse.data.data.forEach(plot => {
                if (plot.isUnlocked) {
                    console.log(`- 区块 ${plot.plotNumber}: 等级 ${plot.level}, 牛奶产量 ${plot.milkProduction}, 生产速度 ${plot.productionSpeed}, 谷仓数量 ${plot.barnCount}`);
                }
            });
        } else {
            console.log('- 没有已解锁的农场区块');
        }
        console.log('');

        // 3. 获取出货线信息
        console.log('🚚 获取出货线信息...');
        const deliveryLineResponse = await axios.get(`${BASE_URL}/delivery-lines`, {
            headers: {
                'wallet-address': TEST_WALLET_ADDRESS,
                'wallet-signature': TEST_SIGNATURE
            }
        });

        console.log('出货线状态:');
        if (deliveryLineResponse.data.data) {
            const dl = deliveryLineResponse.data.data;
            console.log(`- 等级: ${dl.level}`);
            console.log(`- 方块单位: ${dl.blockUnit}`);
            console.log(`- 出货速度: ${dl.deliverySpeed}`);
            console.log(`- 方块价格: ${dl.blockPrice}`);
            console.log(`- 容量: ${dl.capacity}`);
        } else {
            console.log('- 出货线信息不存在');
        }
        console.log('');

        // 4. 预览时间跳跃收益
        console.log('👀 预览1小时时间跳跃收益...');
        try {
            const previewResponse = await axios.get(`${BASE_URL}/iap/time-warp/preview?hours=1`, {
                headers: {
                    'wallet-address': TEST_WALLET_ADDRESS,
                    'wallet-signature': TEST_SIGNATURE
                }
            });

            console.log('预览收益:');
            const preview = previewResponse.data.rewards;
            console.log(`- 预计获得GEM: ${preview.gemsEarned}`);
            console.log(`- 牛奶生产量: ${preview.milkProduced}`);
            console.log(`- 牛奶处理量: ${preview.milkProcessed}`);
            console.log(`- 农场每秒产量: ${preview.farmProductionPerSecond}`);
            console.log(`- 出货线每秒处理量: ${preview.deliveryProcessingPerSecond}`);
            console.log(`- VIP状态: ${preview.hasVip}`);
            console.log(`- 速度加成: ${preview.hasSpeedBoost} (倍数: ${preview.speedBoostMultiplier})`);
        } catch (previewError) {
            console.log('⚠️ 预览时间跳跃收益失败:', previewError.response?.data?.message || previewError.message);
        }
        console.log('');

        // 5. 获取用户拥有的时间跳跃道具
        console.log('📦 获取用户拥有的时间跳跃道具...');
        const boostersResponse = await axios.get(`${BASE_URL}/iap/boosters`, {
            headers: {
                'wallet-address': TEST_WALLET_ADDRESS,
                'wallet-signature': TEST_SIGNATURE
            }
        });

        const timeWarpBoosters = boostersResponse.data.data?.filter(b => b.type === 'time_warp') || [];
        
        if (timeWarpBoosters.length === 0) {
            console.log('⚠️ 用户没有时间跳跃道具');
            console.log('💡 建议先购买时间跳跃道具或使用测试数据');
            return;
        }

        console.log('时间跳跃道具:');
        timeWarpBoosters.forEach(booster => {
            console.log(`- ID: ${booster.id}, 数量: ${booster.quantity}, 持续时间: ${booster.duration}小时`);
        });

        const timeWarpBooster = timeWarpBoosters[0];
        console.log(`\n🎯 选择使用时间跳跃道具: ID ${timeWarpBooster.id}`);
        console.log('');

        // 6. 使用时间跳跃道具
        console.log('🚀 使用时间跳跃道具...');
        console.log('📝 注意查看服务器控制台的调试日志');
        
        const useResponse = await axios.post(`${BASE_URL}/iap/boosters/use`, {
            boosterId: timeWarpBooster.id
        }, {
            headers: {
                'wallet-address': TEST_WALLET_ADDRESS,
                'wallet-signature': TEST_SIGNATURE
            }
        });

        console.log('使用结果:');
        const useResult = useResponse.data;
        console.log(`- 状态: ${useResult.ok ? '成功' : '失败'}`);
        console.log(`- 消息: ${useResult.message}`);
        
        if (useResult.rewards) {
            console.log('- 奖励详情:');
            console.log(`  - 获得GEM: ${useResult.rewards.gemsEarned}`);
            console.log(`  - 牛奶生产: ${useResult.rewards.milkProduced}`);
            console.log(`  - 牛奶处理: ${useResult.rewards.milkProcessed}`);
            console.log(`  - 农场产量/秒: ${useResult.rewards.farmProductionPerSecond}`);
            console.log(`  - 出货处理/秒: ${useResult.rewards.deliveryProcessingPerSecond}`);
        }
        
        if (useResult.debug) {
            console.log('- 调试信息:');
            console.log(`  - 使用前GEM: ${useResult.debug.gemBefore}`);
            console.log(`  - 使用后GEM: ${useResult.debug.gemAfter}`);
            console.log(`  - GEM增加量: ${useResult.debug.gemIncrease}`);
        }
        
        console.log(`- 剩余道具数量: ${useResult.remainingQuantity}`);
        console.log('');

        // 7. 再次获取钱包信息验证
        console.log('🔍 验证GEM余额变化...');
        const newWalletResponse = await axios.get(`${BASE_URL}/wallet`, {
            headers: {
                'wallet-address': TEST_WALLET_ADDRESS,
                'wallet-signature': TEST_SIGNATURE
            }
        });

        const oldGem = walletResponse.data.data.gem;
        const newGem = newWalletResponse.data.data.gem;
        console.log(`原GEM余额: ${oldGem}`);
        console.log(`新GEM余额: ${newGem}`);
        console.log(`实际增加量: ${newGem - oldGem}`);

        if (newGem > oldGem) {
            console.log('✅ GEM余额成功增加！');
        } else {
            console.log('❌ GEM余额没有增加，请检查服务器日志');
        }

    } catch (error) {
        console.error('❌ 测试失败:', error.response?.data || error.message);
    }
}

// 运行测试
testTimeWarpDebug();
