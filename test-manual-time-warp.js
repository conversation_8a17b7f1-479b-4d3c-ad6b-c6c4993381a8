// 测试手动使用时间跳跃道具功能
const axios = require('axios');

const BASE_URL = 'http://localhost:3456/api';

// 测试用的钱包地址和签名（请替换为实际的测试数据）
const TEST_WALLET_ADDRESS = 'EQD4FPq-PRDieyQKkizFTRtSDyucUIqrj0v_zXJmqaDp6_0t';
const TEST_SIGNATURE = 'test_signature';

async function testManualTimeWarp() {
    try {
        console.log('🧪 开始测试手动使用时间跳跃道具功能...\n');

        // 1. 获取用户拥有的道具
        console.log('📦 获取用户拥有的道具...');
        const boostersResponse = await axios.get(`${BASE_URL}/iap/boosters`, {
            headers: {
                'wallet-address': TEST_WALLET_ADDRESS,
                'wallet-signature': TEST_SIGNATURE
            }
        });

        console.log('用户拥有的道具:');
        if (boostersResponse.data.data && boostersResponse.data.data.length > 0) {
            boostersResponse.data.data.forEach(booster => {
                console.log(`- ID: ${booster.id}, 类型: ${booster.type}, 数量: ${booster.quantity}, 持续时间: ${booster.duration}小时`);
            });
        } else {
            console.log('- 暂无道具');
        }
        console.log('');

        // 查找时间跳跃道具
        const timeWarpBoosters = boostersResponse.data.data?.filter(b => b.type === 'time_warp') || [];
        
        if (timeWarpBoosters.length === 0) {
            console.log('⚠️  用户没有时间跳跃道具，无法进行测试');
            console.log('💡 建议先购买时间跳跃道具或使用测试数据');
            return;
        }

        const timeWarpBooster = timeWarpBoosters[0];
        console.log(`🎯 选择使用时间跳跃道具: ID ${timeWarpBooster.id}, 持续时间 ${timeWarpBooster.duration} 小时`);
        console.log('');

        // 2. 预览时间跳跃收益
        console.log('👀 预览时间跳跃收益...');
        const previewResponse = await axios.get(`${BASE_URL}/iap/time-warp/preview?hours=${timeWarpBooster.duration}`, {
            headers: {
                'wallet-address': TEST_WALLET_ADDRESS,
                'wallet-signature': TEST_SIGNATURE
            }
        });

        console.log('预览收益:');
        const preview = previewResponse.data.rewards;
        console.log(`- 预计获得GEM: ${preview.gemsEarned}`);
        console.log(`- 牛奶生产量: ${preview.milkProduced}`);
        console.log(`- 牛奶处理量: ${preview.milkProcessed}`);
        console.log(`- 农场每秒产量: ${preview.farmProductionPerSecond}`);
        console.log(`- 出货线每秒处理量: ${preview.deliveryProcessingPerSecond}`);
        console.log(`- VIP状态: ${preview.hasVip}`);
        console.log(`- 速度加成: ${preview.hasSpeedBoost} (倍数: ${preview.speedBoostMultiplier})`);
        console.log('');

        // 3. 获取当前GEM余额
        console.log('💰 获取当前GEM余额...');
        const walletResponse = await axios.get(`${BASE_URL}/wallet`, {
            headers: {
                'wallet-address': TEST_WALLET_ADDRESS,
                'wallet-signature': TEST_SIGNATURE
            }
        });

        const currentGem = walletResponse.data.data.gem;
        console.log(`当前GEM余额: ${currentGem}`);
        console.log('');

        // 4. 手动使用时间跳跃道具
        console.log('🚀 手动使用时间跳跃道具...');
        const useResponse = await axios.post(`${BASE_URL}/iap/time-warp/use`, {
            boosterId: timeWarpBooster.id
        }, {
            headers: {
                'wallet-address': TEST_WALLET_ADDRESS,
                'wallet-signature': TEST_SIGNATURE
            }
        });

        console.log('使用结果:');
        const useResult = useResponse.data;
        console.log(`- 状态: ${useResult.ok ? '成功' : '失败'}`);
        console.log(`- 消息: ${useResult.message}`);
        if (useResult.rewards) {
            console.log(`- 实际获得GEM: ${useResult.rewards.gemsEarned}`);
            console.log(`- 牛奶生产量: ${useResult.rewards.milkProduced}`);
            console.log(`- 牛奶处理量: ${useResult.rewards.milkProcessed}`);
        }
        console.log(`- 剩余道具数量: ${useResult.remainingQuantity}`);
        console.log('');

        // 5. 验证GEM余额变化
        console.log('🔍 验证GEM余额变化...');
        const newWalletResponse = await axios.get(`${BASE_URL}/wallet`, {
            headers: {
                'wallet-address': TEST_WALLET_ADDRESS,
                'wallet-signature': TEST_SIGNATURE
            }
        });

        const newGem = newWalletResponse.data.data.gem;
        const gemIncrease = newGem - currentGem;
        console.log(`使用后GEM余额: ${newGem}`);
        console.log(`GEM增加量: ${gemIncrease}`);
        console.log('');

        // 6. 再次获取道具列表，验证道具数量变化
        console.log('📦 验证道具数量变化...');
        const newBoostersResponse = await axios.get(`${BASE_URL}/iap/boosters`, {
            headers: {
                'wallet-address': TEST_WALLET_ADDRESS,
                'wallet-signature': TEST_SIGNATURE
            }
        });

        const newTimeWarpBoosters = newBoostersResponse.data.data?.filter(b => b.type === 'time_warp' && b.id === timeWarpBooster.id) || [];
        if (newTimeWarpBoosters.length > 0) {
            const newBooster = newTimeWarpBoosters[0];
            console.log(`道具数量变化: ${timeWarpBooster.quantity} -> ${newBooster.quantity}`);
        } else {
            console.log('道具已完全消耗，记录已删除');
        }

        console.log('\n✅ 手动使用时间跳跃道具功能测试完成！');
        console.log('📝 验证要点:');
        console.log('   - 时间跳跃道具不再自动使用，需要手动触发');
        console.log('   - 使用后道具数量正确减少');
        console.log('   - GEM奖励正确发放');
        console.log('   - 预览收益与实际收益一致');

    } catch (error) {
        console.error('❌ 测试失败:', error.response?.data || error.message);
        
        if (error.response?.status === 404) {
            console.log('💡 可能的原因:');
            console.log('   - 指定的时间跳跃道具不存在');
            console.log('   - 道具数量不足');
            console.log('   - 道具类型不正确');
        }
    }
}

// 运行测试
testManualTimeWarp();
